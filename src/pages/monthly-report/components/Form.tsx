import { useMutation, useQuery } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Spin,
} from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { useAuth } from '@/contexts/auth.tsx'
import { type APIResponse, request } from '@/lib/request.ts'
import type {
  MonthlyReportDTO,
  MonthlyReportFormItem,
} from '@/universal/data-summary/types.ts'

export const MonthlyReportForm = ({
  isUpdate,
  reportMonth = dayjs().format('YYYY-MM'),
  reportId,
}: {
  isUpdate?: boolean
  reportMonth?: string
  reportId?: string
}) => {
  const { user } = useAuth()

  const navigate = useNavigate()

  const [form] = Form.useForm()

  const [lastMonthData, setLastMonthData] = useState<MonthlyReportDTO>()
  const [lastYearData, setLastYearData] = useState<MonthlyReportDTO>()

  const getVerificationResult = (current: number, last: number) => {
    if (current === last) return null
    return current > last ? (
      ''
    ) : (
      <span className="text-[#FF4C4C]">较上月减少</span>
    )
  }

  const getInitializationData = useQuery({
    queryKey: [
      dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      isUpdate ? 1 : 0,
      user?.company_id,
    ],
    queryFn: async ({ queryKey: [period, edit, company_id] }) => {
      const res = await request<APIResponse<{ list: MonthlyReportDTO[] }>>(
        '/monthly-report/save-init',
        {
          query: { period, edit, company_id },
        },
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      setLastMonthData(res.data.list[1])
      setLastYearData(res.data.list[2])

      return res.data.list[0]
    },
  })

  const getDataUpToThisMonth = useQuery({
    queryKey: [
      user?.company_id,
      dayjs(reportMonth).format('YYYY-MM-DD HH:mm:ss'),
    ],
    queryFn: async ({ queryKey: [company_id, report_month] }) => {
      const res = await request<APIResponse<MonthlyReportDTO>>(
        '/monthly-report/base-count',
        { query: { company_id, report_month } },
      )

      if (res.code !== 200001) {
        message.error(res.message)
        return null
      }

      const { period, ...data } = res.data

      form.setFieldsValue({ ...data, period: dayjs(period) })

      return res.data || {}
    },
    enabled: !!user?.company_id,
  })

  const submitForm = useMutation({
    mutationFn: async (values: MonthlyReportFormItem) => {
      const { period, ...data } = values
      const bodyData = {
        ...data,
        period: dayjs(period).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      }

      let res

      if (isUpdate) {
        res = await request<APIResponse<null>>('/monthly-report/modify', {
          method: 'PUT',
          body: { ...getDataUpToThisMonth.data, ...bodyData, id: reportId },
        })
      } else {
        res = await request<APIResponse<null>>('/monthly-report/create', {
          method: 'POST',
          body: bodyData,
        })
      }

      if (res.code === 200001) {
        message.success('操作成功')
        await navigate({ to: '/data-summary/monthly-report' })
        return
      }
      message.error(res.message)
    },
    onError: (err) => message.error(JSON.stringify(err)),
  })

  const watchThisMonthTotalAmount = Form.useWatch('total_amount', form)
  const watchThisMonthFixedAssetsAmount = Form.useWatch(
    'fixed_assets_amount',
    form,
  )
  const watchThisMonthRealEstateAmount = Form.useWatch(
    'real_estate_amount',
    form,
  )
  const watchThisMonthEquityInvestmentAmount = Form.useWatch(
    'equity_investment_amount',
    form,
  )
  const watchThisMonthForeignEquityAmount = Form.useWatch(
    'foreign_equity_amount',
    form,
  )
  const watchThisMonthMainBusinessAmount = Form.useWatch(
    'main_business_amount',
    form,
  )
  const watchThisMonthNonMainBusinessAmount = Form.useWatch(
    'non_main_business_amount',
    form,
  )
  const watchThisMonthDomesticAmount = Form.useWatch('domestic_amount', form)
  const watchThisMonthOverseasAmount = Form.useWatch('overseas_amount', form)
  const watchThisMonthKeyStrategicIndustryAmount = Form.useWatch(
    'key_strategic_industry_amount',
    form,
  )
  const watchThisMonthKeyFixedAssetsAmount = Form.useWatch(
    'key_fixed_assets_amount',
    form,
  )
  const watchThisMonthKeyEquityAmount = Form.useWatch('key_equity_amount', form)
  const watchThisMonthKeyForeignAmount = Form.useWatch(
    'key_foreign_amount',
    form,
  )
  const watchThisMonthKeyManufactureAmount = Form.useWatch(
    'key_manufacture_amount',
    form,
  )
  const watchThisMonthKeyMiningAmount = Form.useWatch('key_mining_amount', form)

  return (
    <Spin spinning={getDataUpToThisMonth.isFetching || submitForm.isPending}>
      <Form form={form} onFinish={submitForm.mutate}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label="填报周期"
              name="period"
              rules={[{ required: true, message: '请选择填报周期' }]}
            >
              <DatePicker
                picker="month"
                className="w-full"
                placeholder="请选择填报周期"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="编制单位"
              name="company_id"
              rules={[{ required: true, message: '请选择编制单位' }]}
            >
              <Select
                placeholder="请选择编制单位"
                options={[{ label: user?.company, value: user?.company_id }]}
                onChange={(_, option) => {
                  const selectedOption = Array.isArray(option)
                    ? option[0]
                    : option
                  form.setFieldValue('company_name', selectedOption?.label)
                }}
              />
            </Form.Item>
            <Form.Item name="company_name" noStyle />
          </Col>
        </Row>

        <Row>
          <table className="border-collapse border border-[#EAEAEA] [&_td]:border [&_td]:border-[#EAEAEA] [&_td]:px-1 [&_td]:text-center [&_td]:text-nowrap">
            <thead>
              <tr className="bg-[#E5EBFE] text-nowrap [&>th]:p-2 [&>th]:font-normal">
                <th colSpan={2}>类型</th>
                <th>截至本月数据(万元)</th>
                <th>截至上月数据(万元)</th>
                <th>去年同期(万元)</th>
                <th>校验结果</th>
                <th>备注说明</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td colSpan={2}>合计</td>
                <td>
                  <Form.Item
                    name="total_amount"
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                    rules={[{ required: true, message: '请输入合计' }]}
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.total_amount}</td>
                <td>{lastYearData?.total_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthTotalAmount,
                    lastMonthData?.total_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="total_amount_remarks"
                    rules={[
                      {
                        required:
                          watchThisMonthTotalAmount <
                          (lastMonthData?.total_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>

              <tr>
                <td rowSpan={4}>投资方式</td>
                <td>固定资产</td>
                <td>
                  <Form.Item
                    name="fixed_assets_amount"
                    rules={[{ required: true, message: '请输入固定资产' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.fixed_assets_amount}</td>
                <td>{lastYearData?.fixed_assets_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthFixedAssetsAmount,
                    lastMonthData?.fixed_assets_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="fixed_assets_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthFixedAssetsAmount <
                          (lastMonthData?.fixed_assets_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:房地产</td>
                <td>
                  <Form.Item
                    name="real_estate_amount"
                    rules={[{ required: true, message: '请输入房地产' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.real_estate_amount}</td>
                <td>{lastYearData?.real_estate_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthRealEstateAmount,
                    lastMonthData?.real_estate_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="real_estate_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthRealEstateAmount <
                          (lastMonthData?.real_estate_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>股权投资</td>
                <td>
                  <Form.Item
                    name="equity_investment_amount"
                    rules={[{ required: true, message: '请输入股权投资' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.equity_investment_amount}</td>
                <td>{lastYearData?.equity_investment_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthEquityInvestmentAmount,
                    lastMonthData?.equity_investment_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="equity_investment_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthEquityInvestmentAmount <
                          (lastMonthData?.equity_investment_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:对外并购</td>
                <td>
                  <Form.Item
                    name="foreign_equity_amount"
                    rules={[{ required: true, message: '请输入对外并购' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.foreign_equity_amount}</td>
                <td>{lastYearData?.foreign_equity_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthForeignEquityAmount,
                    lastMonthData?.foreign_equity_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="foreign_equity_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthForeignEquityAmount <
                          (lastMonthData?.foreign_equity_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={2}>投资方向</td>
                <td>主业</td>
                <td>
                  <Form.Item
                    name="main_business_amount"
                    rules={[{ required: true, message: '请输入主业' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.main_business_amount}</td>
                <td>{lastYearData?.main_business_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthMainBusinessAmount,
                    lastMonthData?.main_business_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="main_business_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthMainBusinessAmount <
                          (lastMonthData?.main_business_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>非主业</td>
                <td>
                  <Form.Item
                    name="non_main_business_amount"
                    rules={[{ required: true, message: '请输入非主业' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.non_main_business_amount}</td>
                <td>{lastYearData?.non_main_business_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthNonMainBusinessAmount,
                    lastMonthData?.non_main_business_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="non_main_business_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthNonMainBusinessAmount <
                          (lastMonthData?.non_main_business_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={2}>投资区域</td>
                <td>境内</td>
                <td>
                  <Form.Item
                    name="domestic_amount"
                    rules={[{ required: true, message: '请输入境内' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.domestic_amount}</td>
                <td>{lastYearData?.domestic_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthDomesticAmount,
                    lastMonthData?.domestic_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="domestic_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthDomesticAmount <
                          (lastMonthData?.domestic_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>境外</td>
                <td>
                  <Form.Item
                    name="overseas_amount"
                    rules={[{ required: true, message: '请输入境外' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.overseas_amount}</td>
                <td>{lastYearData?.overseas_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthOverseasAmount,
                    lastMonthData?.overseas_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="overseas_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthOverseasAmount <
                          (lastMonthData?.overseas_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td rowSpan={6}>重点领域</td>
                <td>战略性新兴产业</td>
                <td>
                  <Form.Item
                    name="key_strategic_industry_amount"
                    rules={[
                      { required: true, message: '请输入战略性新兴产业' },
                    ]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_strategic_industry_amount}</td>
                <td>{lastYearData?.key_strategic_industry_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthKeyStrategicIndustryAmount,
                    lastMonthData?.key_strategic_industry_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="key_strategic_industry_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthKeyStrategicIndustryAmount <
                          (lastMonthData?.key_strategic_industry_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>其中:固定资产投资</td>
                <td>
                  <Form.Item
                    name="key_fixed_assets_amount"
                    rules={[{ required: true, message: '请输入固定资产投资' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_fixed_assets_amount}</td>
                <td>{lastYearData?.key_fixed_assets_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthKeyFixedAssetsAmount,
                    lastMonthData?.key_fixed_assets_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="key_fixed_assets_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthKeyFixedAssetsAmount <
                          (lastMonthData?.key_fixed_assets_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>股权投资</td>
                <td>
                  <Form.Item
                    name="key_equity_amount"
                    rules={[{ required: true, message: '请输入股权投资' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_equity_amount}</td>
                <td>{lastYearData?.key_equity_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthKeyEquityAmount,
                    lastMonthData?.key_equity_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="key_equity_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthKeyEquityAmount <
                          (lastMonthData?.key_equity_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>对外并购</td>
                <td>
                  <Form.Item
                    name="key_foreign_amount"
                    rules={[{ required: true, message: '请输入对外并购' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_foreign_amount}</td>
                <td>{lastYearData?.key_foreign_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthKeyForeignAmount,
                    lastMonthData?.key_foreign_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="key_foreign_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthKeyForeignAmount <
                          (lastMonthData?.key_foreign_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>制造业</td>
                <td>
                  <Form.Item
                    name="key_manufacture_amount"
                    rules={[{ required: true, message: '请输入制造业' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_manufacture_amount}</td>
                <td>{lastYearData?.key_manufacture_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthKeyManufactureAmount,
                    lastMonthData?.key_manufacture_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item
                    name="key_manufacture_amount_remark"
                    rules={[
                      {
                        required:
                          watchThisMonthKeyManufactureAmount <
                          (lastMonthData?.key_manufacture_amount ?? 0),
                        message: '请输入备注说明',
                      },
                    ]}
                  >
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td>采矿业</td>
                <td>
                  <Form.Item
                    name="key_mining_amount"
                    rules={[{ required: true, message: '请输入采矿业' }]}
                    getValueFromEvent={(e) =>
                      e.target.value === '' ? null : Number(e.target.value)
                    }
                  >
                    <Input type="number" />
                  </Form.Item>
                </td>
                <td>{lastMonthData?.key_mining_amount}</td>
                <td>{lastYearData?.key_mining_amount}</td>
                <td>
                  {getVerificationResult(
                    watchThisMonthKeyMiningAmount,
                    lastMonthData?.key_mining_amount ?? 0,
                  )}
                </td>
                <td>
                  <Form.Item name="key_mining_amount_remark">
                    <Input placeholder="请输入" />
                  </Form.Item>
                </td>
              </tr>
              <tr>
                <td colSpan={2} style={{ textAlign: 'left', textWrap: 'wrap' }}>
                  月度分析(简述行业趋势分析及其对投资的影响、投资主要方向及变化、重大项目关键节点进展，150字以内)
                </td>
                <td colSpan={5}>
                  <Form.Item
                    name="monthly_analysis"
                    rules={[{ required: true, message: '请输入月度分析' }]}
                  >
                    <Input.TextArea autoSize={{ minRows: 3, maxRows: 5 }} />
                  </Form.Item>
                </td>
              </tr>
            </tbody>
          </table>
        </Row>
        <Row className="flex justify-end gap-2 py-2">
          <Button type="primary" htmlType="submit">
            {isUpdate ? '更新数据' : '保存草稿'}
          </Button>
          <Button
            onClick={() => navigate({ to: '/data-summary/monthly-report' })}
          >
            取消
          </Button>
        </Row>
      </Form>
    </Spin>
  )
}
